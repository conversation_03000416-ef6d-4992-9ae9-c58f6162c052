inputs = {
  country               = "us"
  environment           = "qa"
  aws_region            = "us-east-1"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "qa"
    }
  )
  root_domain_name      = "dev-nsoc.state911.net"
}

terraform {
  source = "../../../../modules/dns/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
