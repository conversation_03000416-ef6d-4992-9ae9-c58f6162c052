# TODO: Move this to the DNS module
resource "aws_route53_record" "api_cname_record" {
  zone_id = var.domain_hosted_zone_id
  name    = var.api_subdomain_name
  type    = "CNAME"
  ttl     = 300
  records = [aws_lb.collectorapi_alb.dns_name]
}


resource "aws_route53_record" "mariadb_master_cname_record" {
  zone_id = var.domain_hosted_zone_id
  name    = "${local.name_prefix}-comtech-analytics-mariadb-master-internal"
  type    = "CNAME"
  ttl     = 300
  records = [aws_db_instance.mariadb_master_internal.address]
}


resource "aws_route53_record" "mariadb_replica_cname_record" {
  zone_id = var.domain_hosted_zone_id
  name    = "${local.name_prefix}-comtech-analytics-mariadb-replica-internal"
  type    = "CNAME"
  ttl     = 300
  records = [aws_db_instance.mariadb_replica_internal.address]
}
