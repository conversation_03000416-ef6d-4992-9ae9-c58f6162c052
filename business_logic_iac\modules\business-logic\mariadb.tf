resource "aws_db_option_group" "mariadb_option_group" {
  name                     = "${local.name_prefix}-comtech-smartanalytics-mariadb-option-group"
  option_group_description = "Comtech SmartAnalytics custom option group for mariadb"
  engine_name              = "mariadb"
  major_engine_version     = "10.11"

  option {
    option_name = "MARIADB_AUDIT_PLUGIN"
    option_settings {
      name  = "SERVER_AUDIT_EVENTS"
      value = "CONNECT,QUERY"
    }
  }

  tags = {
    Name = "${local.name_prefix}-comtech-smartanalytics-mariadb-option-group"
  }
}


resource "aws_db_subnet_group" "mariadb_db_private_subnet_group" {
  name        = "${local.name_prefix}-comtech-analytics-mariadb-private-subnet-group"
  description = "MariaDB private subnet group"
  subnet_ids  = var.private_subnets_ids

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-mariadb-private-subnet-group"
  }
}


resource "aws_db_instance" "mariadb_master_internal" {
  allocated_storage               = var.mariadb_min_allocated_storage
  max_allocated_storage           = var.mariadb_max_allocated_storage
  apply_immediately               = true
  backup_retention_period         = 7
  ca_cert_identifier              = "rds-ca-rsa4096-g1"
  db_name                         = "GuardianInsights"
  db_subnet_group_name            = aws_db_subnet_group.mariadb_db_private_subnet_group.id
  deletion_protection             = true
  enabled_cloudwatch_logs_exports = ["audit", "error", "general", "slowquery", ]
  engine                          = "mariadb"
  engine_version                  = "10.11.6"
  identifier                      = "${local.name_prefix}-comtech-analytics-mariadb-master-internal"
  instance_class                  = var.mariadb_instance_size
  option_group_name               = aws_db_option_group.mariadb_option_group.name
  username                        = jsondecode(data.aws_secretsmanager_secret_version.mariadb_credentials_current.secret_string)["username"]
  password                        = jsondecode(data.aws_secretsmanager_secret_version.mariadb_credentials_current.secret_string)["password"]
  publicly_accessible             = false
  skip_final_snapshot             = true
  storage_encrypted               = true
  storage_type                    = "gp3"
  vpc_security_group_ids          = [aws_security_group.mariadb_master_sg.id]

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-mariadb-master-internal"
  }
  lifecycle {
    ignore_changes = [
      engine_version,
    ]
  }
}


resource "aws_db_instance" "mariadb_replica_internal" {
  apply_immediately               = true
  backup_retention_period         = 7
  ca_cert_identifier              = "rds-ca-rsa4096-g1"
  deletion_protection             = true
  enabled_cloudwatch_logs_exports = ["audit", "error", "general", "slowquery", ]
  identifier                      = "${local.name_prefix}-comtech-analytics-mariadb-replica-internal"
  instance_class                  = var.mariadb_instance_size
  max_allocated_storage           = var.mariadb_max_allocated_storage
  option_group_name               = aws_db_option_group.mariadb_option_group.name
  publicly_accessible             = false
  replicate_source_db             = aws_db_instance.mariadb_master_internal.identifier
  skip_final_snapshot             = true
  storage_encrypted               = true
  storage_type                    = "gp3"  
  vpc_security_group_ids          = [aws_security_group.mariadb_replica_sg.id]

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-mariadb-replica-internal"
  }
}
