dependency "network" {
  config_path = "../network"
}

inputs = {
  authorization_rules       = {
    local = {
      access_group_id = null
      cidr            = "0.0.0.0/0"
      description     = "Allow inbound route for local VPC"
    }
  }
  aws_region                = "ca-central-1"
  banner_text               = "Welcome to production Comtech smartAnalytics!"
  # Stays the same for deployment in the same AWS account
  certificate_domain_zone   = "nsoc.state911.net"
  # Stays the same for deployment in the same AWS account
  client_cidr_block         = "**********/22"
  cloudwatch_log_group_name = "prod-ca-comtech-smartanalytics-client-vpn-cloudwatch"
  country                   = "ca"  
  description               = "prod-ca-comtech-smartanalytics-client-vpn"
  dns_servers               = [
    cidrhost(dependency.network.outputs.vpc_cidr_block, 2)
  ]
  environment               = "prod"
  # Needs to be unique
  saml_iam_provider_name    = "prod-ca-comtech-smartanalytics-client-vpn-okta"
  saml_metadata_document    = file("prod-ca-metadata.xml")
  subnet_ids                = dependency.network.outputs.private_subnets_ids
  vpc_id                    = dependency.network.outputs.vpc_id

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
    }
  )
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/client-vpn?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
