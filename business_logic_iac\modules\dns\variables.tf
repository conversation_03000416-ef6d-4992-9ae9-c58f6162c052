variable "aws_region" {
  description = "AWS Region"
  type        = string
}


variable "country" {
  description = "Country"
  type        = string
}


variable "environment" {
  description = "Environment"
  type        = string
}


variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources. Defaults to {}."
  default     = {}
}


variable "root_domain_name" {
  description = "The root domain name"
  type        = string
}