# Comtech smartAnalytics: AWS DNS Module

This module will create the dns records for the API's

## Usage

```
module "dns" {
  source                     = "../modules/dns"

  country                    = "us"
  environment                = "dev"
  aws_region                 = "us-east-1"
  root_domain_name           = "your-domain.com"
}
```

NOTE: It is assumed that the root_domain_name value already exists in the Route53 hosted zones.

<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.6 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.24 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.24 |

## Resources
| Name | Type |
|------|------|
| [aws_acm_certificate](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/acm_certificate) | resource |
| [aws_acm_certificate_validation](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/acm_certificate_validation) | resource |
| [aws_route53_record](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_zone](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/route53_zone) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| country | Country | `string` | n/a | yes |
| environment | Environment | `string` | n/a | yes |
| tags | Map of key/value pairs to apply as tags to all resources. Defaults to {}. | `map(string)` | `{}` | no |
| root_domain_name | The root domain name | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| api_acm_arn | API Certificate ARN |
| api_subdomain_name | Subdomain name for the api |
| domain_hosted_zone_id | Domain hosted zone id |