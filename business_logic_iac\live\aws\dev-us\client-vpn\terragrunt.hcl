dependency "network" {
  config_path = "../network"
}

inputs = {
  authorization_rules       = {
    local = {
      access_group_id = null
      cidr            = "0.0.0.0/0"
      description     = "Allow inbound route for local VPC"
    }
  }
  aws_region                = "us-east-1"
  banner_text               = "Welcome to Comtech smartAnalytics!"
  certificate_domain_zone   = "dev-nsoc.state911.net"
  client_cidr_block         = "**********/22"
  cloudwatch_log_group_name = "dev-us-comtech-smartanalytics-client-vpn-cloudwatch"
  country                   = "us"  
  description               = "dev-us-comtech-smartanalytics-client-vpn"
  dns_servers               = [
    cidrhost(dependency.network.outputs.vpc_cidr_block, 2)
  ]
  environment               = "dev"  
  saml_iam_provider_name    = "dev-us-comtech-smartanalytics-client-vpn-okta"
  saml_metadata_document    = file("dev-us-metadata.xml")
  subnet_ids                = dependency.network.outputs.private_subnets_ids
  vpc_id                    = dependency.network.outputs.vpc_id

  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
}

terraform {
  source = "../../../../modules/client-vpn/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
