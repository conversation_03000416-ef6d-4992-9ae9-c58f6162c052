inputs = {
  availability_zones = [
    "us-east-1b",
    "us-east-1c",
  ]
  aws_region  = "us-east-1"
  country     = "us"
  environment = "qa"
  private_subnet_cidrs = [
    "10.1.5.0/24",  # Adjusted CIDR block for QA environment
    "10.1.6.0/24",  # Adjusted CIDR block for QA environment
  ]
  public_subnet_cidrs = [
    "10.1.1.0/24",  # Adjusted CIDR block for QA environment
    "10.1.2.0/24",  # Adjusted CIDR block for QA environment
  ]
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "qa"
    }
  )
  vpc_cidr_block = "10.1.0.0/16"  # Adjusted VPC CIDR block for QA environment
}

terraform {
  source = "../../../../modules/network/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
