inputs = {
  country               = "ca"
  environment           = "prod"
  aws_region            = "ca-central-1"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
    }
  )
  root_domain_name      = "nsoc.state911.net"
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/dns?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
