resource "aws_instance" "power_bi_data_desktop" {
  ami                         = var.aws_power_bi_desktop_ami
  associate_public_ip_address = false
  instance_type               = var.power_bi_desktop_instance_size
  key_name                    = var.key_pair_name
  monitoring                  = true
  subnet_id                   = var.aws_private_subnet[0]
  vpc_security_group_ids      = [aws_security_group.power_bi_desktop_sg.id]

  tags = {
    Name = "${local.name_prefix}-comtech-smartanalytics-power-bi-desktop"
  }
}
