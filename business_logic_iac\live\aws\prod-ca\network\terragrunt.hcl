inputs = {
  availability_zones = [
    "ca-central-1a",
    "ca-central-1b",
  ]
  aws_region  = "ca-central-1"
  country     = "ca"
  environment = "prod"
  private_subnet_cidrs = [
    "10.0.7.0/24",
    "10.0.8.0/24",
  ]
  public_subnet_cidrs = [
    "10.0.3.0/24",
    "10.0.4.0/24",
  ]
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
    }
  )
  vpc_cidr_block = "10.0.0.0/16"
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
