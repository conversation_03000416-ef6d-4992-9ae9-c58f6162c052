resource "aws_instance" "collectorapi" {
  ami                         = var.collectorapi_ami
  associate_public_ip_address = false
  instance_type               = var.collectorapi_instance_size
  iam_instance_profile        = aws_iam_instance_profile.read_only_profile.name
  key_name                    = var.ec2_key_pair_name
  subnet_id                   = var.private_subnets_ids[0]
  vpc_security_group_ids      = [aws_security_group.collectorapi_sg.id]

  metadata_options {
    http_tokens = "required"
  }

  root_block_device {
    volume_size           = "50"
    volume_type           = "gp3"
    encrypted             = true
    delete_on_termination = true
    tags                  = data.aws_default_tags.current.tags
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-collectorapi"
  }

  depends_on = [aws_db_instance.mariadb_master_internal, aws_db_instance.mariadb_replica_internal]
}
