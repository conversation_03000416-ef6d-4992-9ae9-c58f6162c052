# Comtech SmartAnalytics: MariaDB configuration

The following steps document describes how to configure on MariaDB

- Using the client vpn, connect to the MariaDB master node using a database tool like DBeaver
- From the repo [InfoHub](https://dev.azure.com/michaelyee0225/_git/InfoHub?path=/CollectorApi/Solacom.InfoHub.EventReceiver.MariaDb.Context/Sql/0.database_creation.sql&version=GBfeat/SMAN-37/MariaDBWrite&_a=contents) (ensure you have switched to the branch feat/SMAN-37/MariaDBWrite), run the SQL queries contained in the following files in the following order:

	- 0.database_creation.sql
	- 1.table_creation.sql
	- 2.table_updates.sql
	- 3.views.sql
	- 4.procedures.sql
	- 5.user_creation.sql

---

The following steps document describes how to configure a new customer on MariaDB

- Using the client vpn, connect to the MariaDB master node using a database tool like DBeaver
- From the repo [InfoHub](https://dev.azure.com/michaelyee0225/_git/InfoHub?path=/CollectorApi/Solacom.InfoHub.EventReceiver.MariaDb.Context/Sql/0.database_creation.sql&version=GBfeat/SMAN-37/MariaDBWrite&_a=contents) (ensure you have switched to the branch feat/SMAN-37/MariaDBWrite), modify as needed and run the SQL queries contained in the following file:

	- 6.database_onboarding.sql
