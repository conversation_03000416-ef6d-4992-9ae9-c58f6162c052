resource "aws_internet_gateway" "gw" {
  vpc_id = aws_vpc.comtech_analytics.id

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-internet-gateway"
  }
}


resource "aws_eip" "nat_gateway_elastic_ip" {
  domain = "vpc"

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-elastic-ip"
  }

  depends_on = [aws_internet_gateway.gw]
}


resource "aws_nat_gateway" "nat_gateway" {
  allocation_id = aws_eip.nat_gateway_elastic_ip.id
  subnet_id     = aws_subnet.public_subnet[0].id

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-nat-gateway"
  }

  depends_on = [aws_internet_gateway.gw]
}


resource "aws_route_table" "private_route_table" {
  vpc_id = aws_vpc.comtech_analytics.id

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-private-route-table"
  }
}


resource "aws_route_table" "public_route_table" {
  vpc_id = aws_vpc.comtech_analytics.id

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-public-route-table"
  }
}


resource "aws_route" "private_gateway" {
  route_table_id         = aws_route_table.private_route_table.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat_gateway.id
}


resource "aws_route" "public_gateway" {
  route_table_id         = aws_route_table.public_route_table.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.gw.id
}


resource "aws_route_table_association" "private_subnet_association" {
  count          = length(var.private_subnet_cidrs)
  subnet_id      = aws_subnet.private_subnet[count.index].id
  route_table_id = aws_route_table.private_route_table.id
}


resource "aws_route_table_association" "public_subnet_association" {
  count          = length(var.public_subnet_cidrs)
  subnet_id      = aws_subnet.public_subnet[count.index].id
  route_table_id = aws_route_table.public_route_table.id
}
