output "collectorapi_ip" {
  description = "Collectorapi private IP"
  value       = aws_instance.collectorapi.private_ip
}


output "mariadb_master_endpoint" {
  description = "MariaDB master endpiont"
  value       = aws_db_instance.mariadb_master_internal.endpoint
}


output "mariadb_replica_endpoint" {
  description = "MariaDB replica endpiont"
  value       = aws_db_instance.mariadb_replica_internal.endpoint
}


output "mariadb_master_cname" {
  description = "MariaDB master CNAME"
  value       = aws_route53_record.mariadb_master_cname_record.name
}


output "mariadb_replica_cname" {
  description = "MariaDB replica CNAME"
  value       = aws_route53_record.mariadb_replica_cname_record.name
}
