data "aws_iam_policy_document" "read_only_doc" {
  # Allow ec2 instance to read mariadb_credentials from secrets manager
  statement {
    sid    = "SecretsManagerReadOnly"
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:ListSecrets"
    ]
    resources = [
      "*"
    ]
  }

  # Allow ec2 instance to authorize to ECR
  statement {
    sid    = "ElasticContainerRegistryAuthorization"
    effect = "Allow"
    actions = [
      "ecr:GetAuthorizationToken"
    ]
    resources = [
      "*"
    ]
  }

  # Allow ec2 instance to pull collectorapi image from elastic container registry
  statement {
    sid    = "ElasticContainerRegistryReadOnly"
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer"
    ]
    resources = [
      aws_ecr_repository.collectorapi_image.arn
    ]
  }
}


resource "aws_iam_policy" "read_only_policy" {
  name   = "${local.name_prefix}-comtech-analytics-read-only-policy"
  policy = data.aws_iam_policy_document.read_only_doc.json

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-read-only-policy"
  }
}


resource "aws_iam_role" "read_only_role" {
  name = "${local.name_prefix}-comtech-analytics-read-only-role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-read-only-role"
  }
}


resource "aws_iam_role_policy_attachment" "read_only" {
  role       = aws_iam_role.read_only_role.name
  policy_arn = aws_iam_policy.read_only_policy.arn
}


resource "aws_iam_instance_profile" "read_only_profile" {
  name = aws_iam_role.read_only_role.name
  role = aws_iam_role.read_only_role.name

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-read-only-profile"
  }
}
