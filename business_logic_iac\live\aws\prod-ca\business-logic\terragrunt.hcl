dependency "network" {
  config_path = "../network"
}

dependency "dns" {
  config_path = "../dns"
}

inputs = {
  country               = "ca"
  ec2_key_pair_name     = "prod-ca-comtech-analytics"
  environment           = "prod"
  mariadb_instance_size = "db.t4g.large"
  mariadb_max_allocated_storage = 65536
  mariadb_replica_cidr_blocks = ["0.0.0.0/0"]
  private_subnets_ids = dependency.network.outputs.private_subnets_ids
  vpc_cidr_block      = dependency.network.outputs.vpc_cidr_block
  aws_region = "ca-central-1"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
    }
  )
  # prod CollectorAPI AMI ca-central-1
  collectorapi_ami    = "ami-0450c8da0086fc0fa"
  collectorapi_instance_size = "t3a.small"
  vpc_id              = dependency.network.outputs.vpc_id
  api_acm_arn         = dependency.dns.outputs.api_acm_arn
  api_subdomain_name  = dependency.dns.outputs.api_subdomain_name
  domain_hosted_zone_id = dependency.dns.outputs.domain_hosted_zone_id
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/business-logic?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
