resource "aws_security_group" "client_vpn_sg" {
  name        = "${local.name_prefix}-comtech-smartanalytics-client-vpn-sg"
  description = "${local.name_prefix}-comtech-smartanalytics-client-vpn-sg"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${local.name_prefix}-comtech-smartanalytics-client-vpn-sg"
  }
}
