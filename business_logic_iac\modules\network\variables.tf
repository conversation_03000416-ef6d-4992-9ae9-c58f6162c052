variable "availability_zones" {
  description = "The availability zones that the resources will be launched"
  type        = list(string)
}


variable "aws_region" {
  description = "AWS Region"
  type        = string
}


variable "country" {
  description = "Country"
  type        = string
}


variable "environment" {
  description = "Environment"
  type        = string
}


variable "private_subnet_cidrs" {
  description = "A list of private CIDR block for VPC"
  type        = list(string)
}


variable "public_subnet_cidrs" {
  description = "A list of public CIDR block for VPC"
  type        = list(string)
}


variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources. Defaults to {}."
  default     = {}
}


variable "vpc_cidr_block" {
  description = "CIDR block for VPC"
  type        = string
}