resource "aws_security_group" "collectorapi_sg" {
  description = "CollectorAPI security group to allow inbound/outbound from the VPC"
  name        = "${local.name_prefix}-comtech-analytics-collectorapi-sg"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow SSH traffic from VPC CIDR block"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block,
    ]
    from_port = 22
    to_port   = 22
    protocol  = "tcp"
  }

  ingress {
    description     = "Allow inbound traffic from ALB Security Groups"
    security_groups = [aws_security_group.alb_sg.id]
    protocol        = "tcp"
    from_port       = 8888
    to_port         = 8888
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-collectorapi-sg"
  }
}


resource "aws_security_group" "mariadb_master_sg" {
  description = "MariaDB master security group to allow inbound/outbound from the VPC"
  name        = "${local.name_prefix}-comtech-analytics-mariadb-master-sg"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow all inbound traffic from VPC CIDR block"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block,
    ]
    from_port = 0
    to_port   = 0
    protocol  = "-1"
  }

  ingress {
    description     = "Allow all inbound traffic from CollectorAPI Security Groups"
    security_groups = [aws_security_group.collectorapi_sg.id]
    protocol        = "-1"
    from_port       = 0
    to_port         = 0
  }

  ingress {
    description     = "Allow all inbound traffic from MariaDB replica Security Groups"
    security_groups = [aws_security_group.mariadb_replica_sg.id]
    protocol        = "-1"
    from_port       = 0
    to_port         = 0
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-mariadb-master-sg"
  }
}


resource "aws_security_group" "mariadb_replica_sg" {
  description = "MariaDB replica security group to allow inbound/outbound from the VPC"
  name        = "${local.name_prefix}-comtech-analytics-mariadb-replica-sg"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow all inbound traffic from VPC CIDR block"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block,
    ]
    from_port = 0
    to_port   = 0
    protocol  = "-1"
  }

  ingress {
    description = "TCP"
    cidr_blocks = var.mariadb_replica_cidr_blocks
    protocol    = "tcp"
    from_port   = 3306
    to_port     = 3306
  }

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-mariadb-replica-sg"
  }
}

# Security Group for ALB
resource "aws_security_group" "alb_sg" {
  name        = "${local.name_prefix}-comtech-analytics-alb-sg"
  description = "Allow inbound HTTP/HTTPS/SSH traffic to the ALB"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow HTTPS traffic from VPC CIDR block"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block
    ]
  }

  ingress {
    description = "Allow HTTP traffic from VPC CIDR block"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block
    ]
  }

  ingress {
    description = "Allow SSH traffic from VPC CIDR block"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block
    ]
  }

  egress {
    description = "Allow all outbound traffic to the internet"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-alb-sg"
  }
}
