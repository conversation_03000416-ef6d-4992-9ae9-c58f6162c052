module "client_vpn" {
  source = "git::ssh://****************************/cloud-infrastructure/aws-modules.git//modules/client_vpn_saml?ref=rel-6.0.0"

  authorization_rules       = var.authorization_rules
  banner_text               = var.banner_text
  certificate_domain_zone   = var.certificate_domain_zone
  client_cidr_block         = var.client_cidr_block
  cloudwatch_log_group_name = var.cloudwatch_log_group_name
  description               = var.description
  dns_servers               = var.dns_servers
  saml_iam_provider_name    = var.saml_iam_provider_name
  saml_metadata_document    = var.saml_metadata_document
  subnet_ids                = var.subnet_ids
  vpc_id                    = var.vpc_id
  vpn_security_group_ids    = [
    aws_security_group.client_vpn_sg.id
  ]
}
