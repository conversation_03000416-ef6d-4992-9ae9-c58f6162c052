variable "aws_power_bi_data_gateway_ami" {
  description = "Power BI data gateway AMI"
  type        = string
}


variable "aws_private_subnet" {
  description = "Private Subnet block to deploy resources"
  type        = list(string)
}


variable "aws_vpc_id" {
  description = "VPC ID to deploy resources"
  type        = string
}


variable "aws_region" {
  description = "AWS Region"
  type        = string
}


variable "vpc_cidr_block" {
  description = "VPC CIDR block"
  type        = string
}


variable "country" {
  description = "Country"
  type        = string
}


variable "environment" {
  description = "Environment"
  type        = string
}


variable "key_pair_name" {
  description = "Key pair name"
  type        = string
}


variable "power_bi_gateway_instance_size" {
  description = "EC2 instance size for Power BI Data Gateway"
  type        = string
  default     = "t3a.large"
}


variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply as tags to all resources. Defaults to {}."
  default     = {}
}
