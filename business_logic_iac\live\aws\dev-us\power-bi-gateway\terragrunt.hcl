dependency "network" {
  config_path = "../network"
}

inputs = {
  aws_power_bi_data_gateway_ami = "ami-05d3211911c9f9aff"
  aws_private_subnet            = dependency.network.outputs.private_subnets_ids
  aws_region                    = "us-east-1"
  aws_vpc_id                    = dependency.network.outputs.vpc_id
  vpc_cidr_block                = dependency.network.outputs.vpc_cidr_block
  country                       = "us"
  environment                   = "dev"
  key_pair_name                 = "dev-us-comtech-smartanalytics-key-pair"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "dev"
    }
  )
}

terraform {
  source = "../../../../modules/power-bi-gateway/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
