resource "aws_vpc" "comtech_analytics" {
  cidr_block           = var.vpc_cidr_block
  enable_dns_hostnames = true

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-vpc"
  }
}


resource "aws_subnet" "private_subnet" {
  count = length(var.private_subnet_cidrs)

  availability_zone = element(var.availability_zones, count.index)
  cidr_block        = element(var.private_subnet_cidrs, count.index)
  vpc_id            = aws_vpc.comtech_analytics.id

  tags = {
    Name = "${local.name_prefix}-private-subnet-${count.index + 1}"
  }
}


resource "aws_subnet" "public_subnet" {
  count = length(var.public_subnet_cidrs)

  availability_zone       = element(var.availability_zones, count.index)
  cidr_block              = element(var.public_subnet_cidrs, count.index)
  map_public_ip_on_launch = true
  vpc_id                  = aws_vpc.comtech_analytics.id

  tags = {
    Name = "${local.name_prefix}-public-subnet-${count.index + 1}"
  }
}
