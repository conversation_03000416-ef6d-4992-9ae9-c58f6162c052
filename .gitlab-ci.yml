variables:
  DOCKER_TERRAGRUNT_TAG: aws-tf-1.6.4-tg-0.53.6
  PLAN: plan.tfplan
  JSON_PLAN_FILE: tfplan.json
  VAULT_VERSION: 1.13.2
  TRIVY_VERSION: 0.47.0
  CHECKSUMS: |-
    f7930279de8381de7c532164b4a4408895d9606c0d24e2e9d2f9acb5dfe99b3c  /tmp/vault.zip
  TF_PLUGIN_CACHE_DIR: ${CI_PROJECT_DIR}/.terraform.d/plugin-cache
  TFLINT_PLUGIN_DIR: ${CI_PROJECT_DIR}/.tflint.d/plugins

# See https://docs.gitlab.com/ee/ci/yaml/workflow.html#switch-between-branch-pipelines-and-merge-request-pipelines
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^rel-/

default:
  cache:
    - key:
      paths:
        - ${CI_PROJECT_DIR}/.tflint.d/plugins
        - ${CI_PROJECT_DIR}/.terraform.d/plugin-cache
  image: 
    name: devopsinfra/docker-terragrunt:$DOCKER_TERRAGRUNT_TAG
    pull_policy:
      - if-not-present
      - always
  before_script:
    - mkdir -p ${CI_PROJECT_DIR}/.terraform.d/plugin-cache || true
    - mkdir -p ${CI_PROJECT_DIR}/.tflint.d/plugins || true
    # Alias for terraform plan integration with Gitlab MR.
    - shopt -s expand_aliases
    - alias convert_report="jq -r '([.resource_changes[]?.change.actions?]|flatten)|{\"create\":(map(select(.==\"create\"))|length),\"update\":(map(select(.==\"update\"))|length),\"delete\":(map(select(.==\"delete\"))|length)}'"
    # Install Vault
    - curl -L -o /tmp/vault.zip "https://releases.hashicorp.com/vault/${VAULT_VERSION}/vault_${VAULT_VERSION}_linux_amd64.zip"
    - sha256sum /tmp/vault.zip
    - echo ${CHECKSUMS}
    - echo ${CHECKSUMS} | (cd /tmp; sha256sum -c -w -) || exit 1
    - unzip /tmp/vault.zip -d /tmp
    - install -b -c -v /tmp/vault /usr/bin/
    # Dump versions & info
    - terragrunt -version
    - terraform -version
    - tflint --version
    - echo ${CI_PROJECT_NAMESPACE}
    - echo ${CI_PROJECT_NAME}
    # Set Git to use CI Token for cloning.
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.nsoc.state911.net".insteadOf "ssh://****************************"

stages:
  - scan
  - plan
  - deploy

trivy:
  stage: scan
  image: 
    name: aquasec/trivy:${TRIVY_VERSION}
    entrypoint: [""]
  before_script: ""
  script:
    - for dir in $(ls business_logic_iac/modules) ; do trivy filesystem --scanners config,vuln business_logic_iac/modules/$dir; done;
  after_script: ""

.plan-template:
  stage: plan
  resource_group: terraform_state
  rules:
    - when: never
  id_tokens:
    VAULT_TOKEN:
      aud: https://vault.int.state911.net
  script:
    - export VAULT_TOKEN="$(vault write -field=token auth/jwt/login role=ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME} jwt=${VAULT_TOKEN})"
    # Setup default profile with region. Creds come from environment variables
    - mkdir -p ${HOME}/.aws/ || true
    - (echo "[default]"; echo "region = ${REGION}";) > ${HOME}/.aws/config
    # Pull credentials and assume target role.
    - export AWS_ACCESS_KEY_ID=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.access_key)
    - export AWS_SECRET_ACCESS_KEY=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.secret_access_key)
    - export ROLE_ARN=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r ".data.data.ci_role_arn_list[\"$ACCOUNT\"]")
    # Change to environment directory and run terragrunt
    - cd business_logic_iac/live/${PARTITION}/${ENVIRONMENT}/${MODULE}
    - terragrunt render-json --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - cat terragrunt_rendered.json | jq .
    - terragrunt validate --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - terragrunt plan --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" -out $(pwd)/${ENVIRONMENT}-${MODULE}-${PLAN}
    - terragrunt show --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" --json $(pwd)/${ENVIRONMENT}-${MODULE}-${PLAN} | convert_report > $(pwd)/${ENVIRONMENT}-${MODULE}-${JSON_PLAN_FILE}
  after_script:
    - rm -rf ${HOME}/.aws/
  artifacts:
    name: terraform-plan
    expire_in: 1 week
    paths:
      - business_logic_iac/live/${PARTITION}/${ENVIRONMENT}/${MODULE}/${ENVIRONMENT}-${MODULE}-${PLAN}
      - business_logic_iac/live/${PARTITION}/${ENVIRONMENT}/${MODULE}/.terragrunt-cache/
    reports:
      terraform: business_logic_iac/live/${PARTITION}/${ENVIRONMENT}/${MODULE}/${ENVIRONMENT}-${MODULE}-${JSON_PLAN_FILE}

plan-dev-us-network:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: network
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-power-bi-gateway:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: power-bi-gateway
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-power-bi-desktop:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: power-bi-desktop
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-business-logic:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: business-logic
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-client-vpn:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: client-vpn
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-us-dns:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: dns
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-us-dns:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: dns
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-us-network:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: network
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-us-client-vpn:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: client-vpn
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-us-business-logic:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: business-logic
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-us-power-bi-gateway:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: power-bi-gateway
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-us-power-bi-desktop:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: power-bi-desktop
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-ca-dns:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: dns
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-ca-network:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: network
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-ca-client-vpn:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: client-vpn
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-ca-business-logic:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: business-logic
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-ca-power-bi-gateway:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: power-bi-gateway
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-ca-power-bi-desktop:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: power-bi-desktop
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

.deploy-template:
  stage: deploy
  resource_group: terraform_state
  rules:
    - when: never
  id_tokens:
    VAULT_TOKEN:
      aud: https://vault.int.state911.net
  script:
    - export VAULT_TOKEN="$(vault write -field=token auth/jwt/login role=ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME} jwt=${VAULT_TOKEN})"
    # Setup default profile with region. Creds come from environment variables
    - mkdir -p ${HOME}/.aws/ || true
    - (echo "[default]"; echo "region = ${REGION}";) > ${HOME}/.aws/config
    # Pull credentials and assume target role.
    - export AWS_ACCESS_KEY_ID=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.access_key)
    - export AWS_SECRET_ACCESS_KEY=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.secret_access_key)
    - export ROLE_ARN=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r ".data.data.ci_role_arn_list[\"$ACCOUNT\"]")
    # Change to environment directory and run terragrunt
    - cd business_logic_iac/live/${PARTITION}/${ENVIRONMENT}/${MODULE}
    - terragrunt render-json --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - cat terragrunt_rendered.json | jq .
    - terragrunt apply --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" $(pwd)/${ENVIRONMENT}-${MODULE}-${PLAN}
  after_script:
    - rm -rf ${HOME}/.aws/

deploy-dev-us-network:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: network
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-network

deploy-dev-us-power-bi-gateway:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: power-bi-gateway
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-power-bi-gateway

deploy-dev-us-power-bi-desktop:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: power-bi-desktop
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-power-bi-desktop    

deploy-dev-us-business-logic:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: business-logic
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-business-logic

deploy-dev-us-client-vpn:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: client-vpn
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-client-vpn

deploy-dev-us-dns:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-us
    MODULE: dns
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-us-dns

plan-qa-us-network:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: network
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-qa-us-power-bi-gateway:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: power-bi-gateway
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-qa-us-power-bi-desktop:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: power-bi-desktop
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-qa-us-business-logic:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: business-logic
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-qa-us-client-vpn:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: client-vpn
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-qa-us-dns:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: dns
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "cloud-infrastructure" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

deploy-qa-us-network:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: network
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: testing
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-qa-us-network

deploy-qa-us-power-bi-gateway:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: power-bi-gateway
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: testing
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-qa-us-power-bi-gateway

deploy-qa-us-power-bi-desktop:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: power-bi-desktop
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: testing
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-qa-us-power-bi-desktop  

deploy-qa-us-business-logic:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: business-logic
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: testing
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-qa-us-business-logic

deploy-qa-us-client-vpn:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: client-vpn
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: testing
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-qa-us-client-vpn

deploy-qa-us-dns:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: qa-us
    MODULE: dns
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: testing
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-qa-us-dns

deploy-prod-us-network:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: network
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-us-network

deploy-prod-us-dns:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: dns
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-us-dns

deploy-prod-us-client-vpn:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: client-vpn
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-us-client-vpn

deploy-prod-us-business-logic:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: business-logic
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-us-business-logic

deploy-prod-us-power-bi-gateway:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: power-bi-gateway
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-us-power-bi-gateway

deploy-prod-us-power-bi-desktop:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-us
    MODULE: power-bi-desktop
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-us-power-bi-desktop

deploy-prod-ca-network:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: network
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-ca-network

deploy-prod-ca-dns:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: dns
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-ca-dns

deploy-prod-ca-client-vpn:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: client-vpn
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-ca-client-vpn

deploy-prod-ca-business-logic:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: business-logic
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-ca-business-logic

deploy-prod-ca-power-bi-gateway:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: power-bi-gateway
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-ca-power-bi-gateway

deploy-prod-ca-power-bi-desktop:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-ca
    MODULE: power-bi-desktop
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}-${MODULE}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "cloud-infrastructure"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-ca-power-bi-desktop