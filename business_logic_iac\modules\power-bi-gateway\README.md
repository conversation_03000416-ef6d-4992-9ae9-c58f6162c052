# Comtech SmartAnalytics: Power BI Gateway Module

This module will create the resources required to deploy the power BI gateway

## Usage

```
module "power_bi_gateway" {
  source = "../../../../modules/power-bi-gateway"

  aws_power_bi_data_gateway_ami = "aws_power_bi_data_gateway_ami"
  aws_private_subnet            = [aws_private_subnet]
  aws_vpc_id                    = "aws_vpc_id"
  country                       = "country"
  environment                   = "environment"
  key_pair_name                 = "key_pair_name"
}
```


<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.6 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.24 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.24 |

## Resources

| Name | Type |
|------|------|
| [aws_autoscaling_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/autoscaling_group) | resource |
| [aws_launch_template](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/launch_template) | resource |
| [aws_security_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |

## Inputs

|Inputs| Name | Description | Type | Default | Required |
|------|------|-------------|------|---------|----------|
| aws_power_bi_data_gateway_ami | n/a | Power BI Gateway AMI for the specific region | `string`  |  | yes |
| aws_private_subnet | n/a | A list of public subnets | `list(string)` |  |yes |
| aws_region | n/a | AWS region | `string`  |  | yes |
| aws_vpc_id | n/a | AWS VPC ID | `string`  |  | yes |
| country | n/a | County | `string` |  | yes |
| environment | n/a | Environment | `string`  |  | yes |
| key_pair_name | n/a | Key pair name | `string`  |  | yes |
| tags | n/a | Map of key/value pairs to apply as tags to all resources | `string`  |  | yes |
| vpc_cidr_block | n/a | VPC CIDR block | `string`  |  | yes |

## Outputs

| Outputs| Name | Description |
|--------|------|-------------|
| power_bi_data_gateway_asg_sg | n/a | Power BI data gateway ASG security group |
<!--- END_TF_DOCS --->
