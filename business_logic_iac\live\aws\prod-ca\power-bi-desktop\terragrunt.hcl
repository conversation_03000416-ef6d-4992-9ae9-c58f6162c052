dependency "network" {
  config_path = "../network"
}

inputs = {
  aws_power_bi_desktop_ami = "ami-0cdf7919fe2475399"
  power_bi_desktop_instance_size = "t3a.large"
  aws_private_subnet       = dependency.network.outputs.private_subnets_ids
  aws_region               = "ca-central-1"
  aws_vpc_id               = dependency.network.outputs.vpc_id
  vpc_cidr_block           = dependency.network.outputs.vpc_cidr_block
  country                  = "ca"
  environment              = "prod"
  key_pair_name            = "prod-ca-comtech-analytics"
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "ca"
      environment = "prod"
    }
  )
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/power-bi-desktop?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
