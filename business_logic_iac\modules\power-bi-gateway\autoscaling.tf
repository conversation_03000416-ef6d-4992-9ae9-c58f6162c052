resource "aws_launch_template" "power_bi_data_gateway_launch_template" {
  name                   = "${local.name_prefix}-comtech-smartanalytics-power-bi-data-gateway-launch-template"
  image_id               = var.aws_power_bi_data_gateway_ami
  instance_type          = var.power_bi_gateway_instance_size
  update_default_version = true
  key_name               = var.key_pair_name

  block_device_mappings {
    device_name = "/dev/xvdb"
    ebs {
      delete_on_termination = true
      encrypted             = true
      volume_type           = "gp3"
      volume_size           = 100
    }
  }

  network_interfaces {
    device_index    = 0
    security_groups = [aws_security_group.power_bi_data_gateway_asg_sg.id]
  }

  tags = {
    Name = "${local.name_prefix}-comtech-smartanalytics-power-bi-data-gateway-launch-template"
  }
}


resource "aws_autoscaling_group" "power_bi_data_gateway_auto_scaling_group" {
  desired_capacity    = 1
  max_size            = 1
  min_size            = 1
  vpc_zone_identifier = var.aws_private_subnet
  name                = "${local.name_prefix}-comtech-smartanalytics-power-bi-data-gateway-auto-scaling-group"

  launch_template {
    id      = aws_launch_template.power_bi_data_gateway_launch_template.id
    version = aws_launch_template.power_bi_data_gateway_launch_template.latest_version
  }
}
