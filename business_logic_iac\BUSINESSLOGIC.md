# Comtech SmartAnalytics: Business logic configuration

Prerequisites:

- Customer name
- Customer short
- Customer tenant names
- Key pair PEM file
- AWS credentials to the DEV-NSOC account

The following document describes how to deploy the configure business logic application

- From the repo [InfoHub](https://dev.azure.com/michaelyee0225/_git/InfoHub?path=/CollectorApi/Solacom.InfoHub.EventReceiver.MariaDb.Context/Sql/0.database_creation.sql&version=GBfeat/SMAN-37/MariaDBWrite&_a=contents) (ensure you have switched to the branch feat/SMAN-37/MariaDBWrite), update the appettings files and run the following commands in the ..\InfoHub\CollectorApi folder:

```
docker build -t ************.dkr.ecr.us-east-1.amazonaws.com/dev-us-comtech-analytics-ecr-collectorapi -f Solacom.InfoHub.EventReceiver.Web.API\Dockerfile .

aws ecr get-login-password --profile dev-nsoc --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com


docker push ************.dkr.ecr.us-east-1.amazonaws.com/dev-us-comtech-analytics-ecr-collectorapi:latest
```
NOTE: You must manually delete the latest docker image in the ECR

- Using the client vpn, connect to the business logic ec2 instance using ssh
- Run the following commands:

```
aws ecr get-login-password --region us-east-1 | sudo docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com

sudo docker pull ************.dkr.ecr.us-east-1.amazonaws.com/dev-us-comtech-analytics-ecr-collectorapi:latest
```

- Create a file name docker-compose.yml containing the following data:

```
      version: "3.8"

      services:

        collectorapi:
          container_name: collectorapi
          hostname: collectorapi
          image: ************.dkr.ecr.us-east-1.amazonaws.com/dev-us-comtech-analytics-ecr-collectorapi:latest
          restart: unless-stopped
          entrypoint: ["dotnet", "Solacom.InfoHub.EventReceiver.Web.API.dll"]
          ports:
            - 8888:80    
          healthcheck:
            test: 
              [ "CMD-SHELL",
                "curl http://localhost/api/health/ping",
              ]
            interval: 60s
            timeout: 10s
            retries: 5
```

- Run the following command

```
sudo docker compose up -d
```

Example appsettings.json
```
{
  "version": "c43ad438 - Concurrent end events + New database",
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Warning",
      "Microsoft": "Warning"
    },
    "elasticsearchSettings": {
      "serilog": {
        "enableSSL": "true",
        "restrictedToMinimumLevel": "Information",
        "indexFormat": "application_logs_guardian_insights-{0:yyyy.MM.dd}",
        "autoRegisterTemplate": false,
        "autoRegisterTemplateVersion": "ESv7"
      }
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "Properties": {
      "ApplicationName": "CollectorAPI",
      "Instance": "ComtechSmartAnalytics"
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "restrictedToMinimumLevel": "Debug",
          "path": "Logs/logs_.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 31
        }
      }
    ]
  },
  "clientSettings": {
    "clients": "pv-gatineau,pv-legacy-gatineau",
    "clientcodeIndexPrefix": {
      "pvgt": "pv-gatineau",
      "pvlggt": "pv-legacy-gatineau"
    },
    "clientTenantMapping": {
      "pvgt": {
        "tng000": "tng000",
        "tng001": "tng001",
        "map": "map"
      },
      "pvlggt": {
        "tng000": "tng000",
        "tng001": "tng001",
        "map": "map"
      }
    },
    "clientTimezoneMapping": {
      "pvgt": "America/New_York",
      "pvlggt": "America/New_York"
    },
    "clientEsiNetSupportedList": "pvgt"
  },
  "classofservice": {
    "Values": {
      "RESD": "Landline",
      "BUSN": "Landline",
      "WPH1": "Wireless",
      "WPH2": "Wireless",
      "WRLS": "Wireless",
      "VOIP": "Voip",
      "PBXA": "Landline",
      "PBXB": "Landline",
      "PBXC": "Landline",
      "CNTX": "Landline",
      "BSNX": "Landline",
      "MOBL": "Wireless",
      "OMBL": "Wireless",
      "VRES": "Voip",
      "VPAY": "Voip",
      "VBUS": "Voip",
      "COCT": "Landline",
      "RSDX": "Landline",
      "COIN": "Landline",
      "RESX": "Landline",
      "BUSX": "Landline",
      "P2P1": "Wireless",
      "NA": "Unknown",
      "PAY$": "Landline",
      "VMBL": "Voip",
      "AFF": "Landline",
      "BOP": "Landline",
      "BUS": "Landline",
      "CCC": "Landline",
      "CCX": "Landline",
      "CEL": "Wireless",
      "CIP": "Voip",
      "COM": "Landline",
      "CPB": "Landline",
      "CTX": "Landline",
      "DAT": "Landline",
      "DEA": "Landline",
      "DID": "Landline",
      "DRT": "Wireless",
      "FEX": "Landline",
      "HDC": "Landline",
      "LRR": "Landline",
      "MIP": "Voip",
      "MUL": "Landline",
      "OUT": "Landline",
      "PCS": "Wireless",
      "PUB": "Landline",
      "PUC": "Landline",
      "RES": "Landline",
      "REV": "Landline",
      "ROP": "Landline",
      "SPP": "Landline",
      "TXE": "Wireless",
      "TXF": "Wireless",
      "VEL": "Landline",
      "VIP": "Landline",
      "WL2": "Wireless"
    }
  },
  "userKeyData": {
    "userId": <SECRET>,
    "hashedApiKey": <SECRET>,
    "rawApiKey": <SECRET>
  },
  "scheduledServicesFrequenciesInMinutes": {
    "expiredEventService": 1,
    "dbCleanupService": 180,
    "processQueueservice": 1
  },
  "expiredEventInHours": 8,
  "AllowedHosts": "*",
  "Database": {
    "CleanupOlderThanInHours": {
      "hashevents": 48,
      "events": 48,
      "agentsession": 48
    }
  }
}
```

Example appsettings.Production.json

```
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Warning",
      "Microsoft": "Warning"
    },
    "elasticsearchSettings": {
      "url": <SECRET>,
      "userName": <SECRET>,
      "password": <SECRET>
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "Properties": {
      "Environment": "DEV-NSOC"
    }
  },
  "Database": {
    "mysql": {
      "connectionstring.CollectorAPI": <SECRET>,
      "connectionstring.InsightsData": <SECRET>"
    }
  }
}
```
