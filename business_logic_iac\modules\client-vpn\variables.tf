variable "authorization_rules" {
  type = map(object({
    access_group_id = any
    cidr            = string
    description     = string
  }))
  description = "Map of authorization rule objects."
}


variable "aws_region" {
  description = "AWS Region"
  type        = string
}


variable "banner_text" {
  type        = string
  description = "Banner text to display to Amazon Client VPN client software users.  Defaults to null."
  nullable    = true
  default     = "Welcome to Comtech smartAnalytics!"
}


variable "certificate_domain_zone" {
  type        = string
  description = "Route 53 public zone name for ACM certificate validation.  Client VPN server certificate will be generated with a randomized domain name under this domain."
}


variable "client_cidr_block" {
  type        = string
  description = "CIDR block to use for client connections.  Must be at least a /22 and not overlap with other CIDRs in use in network."
}


variable "cloudwatch_log_group_name" {
  type        = string
  description = "Name of the CloudWatch Logs group to create for connection logs."
}


variable "description" {
  type        = string
  description = "Description string to use on Client VPN endpoint."
}


variable "dns_servers" {
  type        = list(string)
  description = "List of DNS server IPs."
}


variable "country" {
  description = "Country"
  type        = string
}


variable "environment" {
  description = "Environment"
  type        = string
}


variable "saml_iam_provider_name" {
  type        = string
  description = "Name of the IAM SAML provider to create."
}


variable "saml_metadata_document" {
  type        = string
  description = "Contents of the SAML IdP's metadata document."
}


variable "subnet_ids" {
  type        = list(string)
  description = "Subnet IDs to attach to Client VPN."
}


variable "tags" {
  type        = map(string)
  description = "Map of key/value pairs to apply to resources as tags.  Defaults to {}."
  default     = {}
}


variable "vpc_id" {
  type        = string
  description = "VPC ID of the target VPC."
}
