# Comtech smartAnalytics: AWS Client VPN Module

This module will create the required Security Group resource for AWS Client VPN 

## Usage

```
module "client_vpn" {
  source = "git::ssh://****************************/cloud-infrastructure/aws-modules.git//modules/client_vpn_saml?ref=rel-5.7.0"

  authorization_rules       = var.authorization_rules
  banner_text               = var.banner_text
  certificate_domain_zone   = var.certificate_domain_zone
  client_cidr_block         = var.client_cidr_block
  cloudwatch_log_group_name = var.cloudwatch_log_group_name
  country                   = var.country
  description               = var.description
  dns_servers               = var.dns_servers
  environment               = var.environment
  saml_iam_provider_name    = var.saml_iam_provider_name
  saml_metadata_document    = var.saml_metadata_document
  subnet_ids                = var.subnet_ids
  vpc_id                    = var.vpc_id
  vpn_security_group_ids    = [
    aws_security_group.client_vpn_sg.id
  ]
}
```

<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.6 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.24 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.24 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="client_vpn_saml"></a> [aws](#module\_client-vpn) | [Client VPN Docs](https://gitlab.nsoc.state911.net/cloud-infrastructure/aws-modules#client_vpn_saml) | n/a |

## Inputs

|Inputs| Name | Description | Type | Default | Required |
|------|------|-------------|------|---------|----------|
| authorization_rules | n/a | Map of authorization rule objects | `map` |  | yes |
| banner_text | n/a | Banner text to display to Amazon Client VPN client software users | `string` |  | yes |
| certificate_domain_zone | n/a | Route 53 public zone name for ACM certificate validation | `string` |  | yes |
| client_cidr_block | n/a | CIDR block to use for client connections | `string` |  | yes |
| cloudwatch_log_group_name | n/a | Name of the CloudWatch Logs group to create for connection logs | `string` |  | yes |
| country | n/a | County | `string` |  | yes |
| description | n/a | Description string to use on Client VPN endpoint | `string` |  | yes |
| dns_servers | n/a | List of DNS server IPs | `list(string)` |  | yes |
| environment | n/a | Environment | `string`  |  | yes |
| saml_iam_provider_name | n/a | Name of the IAM SAML provider to create | `string` |  | yes |
| saml_metadata_document | n/a | Contents of the SAML IdP's metadata document | `string` |  | yes |
| subnet_ids | n/a | Subnet IDs to attach to Client VPN | `list(string)` |  | yes |
| tags | n/a | Map of key/value pairs to apply as tags to all resources | `string`  |  | yes |
| vpc_id | n/a | VPC ID of the target VPC | `string`  |  | yes |

## Outputs

| Outputs| Name | Description |
|--------|------|-------------|
| aws_client_vpn_security_group | n/a | AWS vpn security group |
<!--- END_TF_DOCS --->
