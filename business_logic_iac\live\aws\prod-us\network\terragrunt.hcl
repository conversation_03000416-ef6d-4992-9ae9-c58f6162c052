inputs = {
  availability_zones = [
    "us-east-1b",
    "us-east-1c",
  ]
  aws_region  = "us-east-1"
  country     = "us"
  environment = "prod"
  private_subnet_cidrs = [
    "10.0.5.0/24",
    "10.0.6.0/24",
  ]
  public_subnet_cidrs = [
    "10.0.1.0/24",
    "10.0.2.0/24",
  ]
  tags = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "prod"
    }
  )
  vpc_cidr_block = "10.0.0.0/16"
}

terraform {
  source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/network?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
