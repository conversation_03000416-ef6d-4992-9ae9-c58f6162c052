resource "aws_acm_certificate" "comtech_smartanalytics_certificate" {
  domain_name       = local.api_subdomain_name
  validation_method = "DNS"

  options {
    certificate_transparency_logging_preference = "DISABLED"
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-certificate"
  }
}


resource "aws_acm_certificate_validation" "comtech_smartanalytics_certificate_validation" {
  certificate_arn         = aws_acm_certificate.comtech_smartanalytics_certificate.arn
  validation_record_fqdns = [for record in aws_route53_record.comtech_smartanalytics_public_record : record.fqdn]
}