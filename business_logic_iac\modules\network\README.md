# Comtech Analytics Networking Module

## Background

This module will create a basic AWS VPC using Terraform

## Usage

```tf
module "vpc" {
  source                     = "../modules/vpc"

  availability_zones         = ["us-west-1a", "us-west-1b"]
  country                    = "us"
  environment                = "prd"
  private_subnet_cidrs       = ["********/24", "********/24"]
  public_subnet_cidrs        = ["********/24", "********/24"]
  tags                       = { "Project" = "Comtech Analytics", "Environment" = "Production" }
  vpc_cidr_block             = "10.0.0.0/16"
}
```

<!--- BEGIN_TF_DOCS --->
## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.6 |
| aws | >= 5.24.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.24.0 |

## Modules

N/A

## Resources

| Name | Type |
|------|------|
| [aws_eip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip) | resource |
| [aws_internet_gateway](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/internet_gateway) | resource |
| [aws_nat_gateway](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/nat_gateway) | resource |
| [aws_route](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route) | resource |
| [aws_route_table](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table) | resource |
| [aws_route_table_association](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association) | resource |
| [aws_security_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_subnet](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet) | resource |
| [aws_vpc](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| availability_zones | The availability zones that the resources will be launched in. | `list(string)` | n/a | yes |
| country | Country where the resources will be deployed. | `string` | n/a | yes |
| environment | Deployment environment (e.g., dev, prd). | `string` | n/a | yes |
| private_subnet_cidrs | A list of private CIDR blocks for the VPC. | `list(string)` | n/a | yes |
| public_subnet_cidrs | A list of public CIDR blocks for the VPC. | `list(string)` | n/a | yes |
| tags | Map of key/value pairs to apply as tags to all resources. | `map(string)` | `{}` | no |
| vpc_cidr_block | CIDR block for the VPC. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| private_subnets_ids | IDs of the private subnets created. |
| public_subnets_ids | IDs of the public subnets created. |
| vpc_id | ID of the VPC created. |
| vpc_cidr_block | CIDR block of the VPC created. |
<!--- END_TF_DOCS --->