resource "aws_security_group" "power_bi_desktop_sg" {
  description = "Power BI desktop security group"
  name        = "${local.name_prefix}-comtech-smartanalytics-power-bi-desktop-security-group"
  vpc_id      = var.aws_vpc_id

  ingress {
    description = "Allow all inbound traffic from VPC CIDR block"
    cidr_blocks = [
      # VPC CIDRs
      var.vpc_cidr_block,
    ]
    from_port = 0
    to_port   = 0
    protocol  = "-1"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${local.name_prefix}-comtech-smartanalytics-power-bi-desktop-security-group"
  }
}
