# ALB
resource "aws_lb" "collectorapi_alb" {
  name                       = "${local.name_prefix}-comtech-analytics-alb"
  internal                   = true
  load_balancer_type         = "application"
  security_groups            = [aws_security_group.alb_sg.id]
  subnets                    = var.private_subnets_ids
  enable_deletion_protection = false

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-alb"
  }
}

# Target Group for the EC2 instance
resource "aws_lb_target_group" "collectorapi_tg" {
  name        = "${local.name_prefix}-comtech-analytics-api-tg"
  port        = local.collectorapi_port
  protocol    = "HTTP"
  target_type = "instance"
  vpc_id      = var.vpc_id

  health_check {
    enabled             = true
    path                = "/api/health/ping"
    protocol            = "HTTP"
    matcher             = "200"
    port                = "traffic-port"
    interval            = 30
    timeout             = 5
    healthy_threshold   = 5
    unhealthy_threshold = 2
  }

  tags = {
    Name = "${local.name_prefix}-comtech-analytics-api-tg"
  }
}


resource "aws_lb_listener" "http_listener" {
  load_balancer_arn = aws_lb.collectorapi_alb.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}


resource "aws_lb_listener" "https_listener" {
  load_balancer_arn = aws_lb.collectorapi_alb.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = var.api_acm_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.collectorapi_tg.arn
  }
}


resource "aws_lb_target_group_attachment" "collectorapi_attachment" {
  target_group_arn = aws_lb_target_group.collectorapi_tg.arn
  target_id        = aws_instance.collectorapi.id
  port             = local.collectorapi_port
}
